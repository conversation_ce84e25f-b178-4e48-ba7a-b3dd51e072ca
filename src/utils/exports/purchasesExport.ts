// Professional Excel export functionality for Purchases, with customizable column widths

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  ExcelCellStyle,
} from "../excelStyles";

// --- INTERFACES ---

interface ColumnConfig {
  key: string;
  label: string;
  type: string;
  width?: number; // Custom width in characters
  formatter?: (value: any) => any;
}

interface ExportOptions {
  companyName?: string;
  reportTitle?: string;
  customColumnWidths?: { [columnKey: string]: number }; // Map column keys to widths
  useAutoWidth?: boolean; // Default: false (use manual widths)
}

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- COLUMN WIDTH CALCULATION ---

const calculateColumnWidths = (
  columns: ColumnConfig[],
  data: any[],
  options: ExportOptions = {}
): { wch: number }[] => {
  return columns.map((col, index) => {
    // 1. Check for custom width in options
    if (options.customColumnWidths && options.customColumnWidths[col.key]) {
      return { wch: options.customColumnWidths[col.key] };
    }

    // 2. Check for predefined width in column config
    if (col.width) {
      return { wch: col.width };
    }

    // 3. Use auto-calculation if enabled (fallback)
    if (options.useAutoWidth) {
      const headerLength = col.label.length;
      const maxDataLength =
        data.length > 0
          ? Math.max(
              ...data.map((row) => {
                const value = getNestedValue(row, col.key);
                return String(value || "").length;
              })
            )
          : 0;
      return {
        wch: Math.min(60, Math.max(headerLength + 5, maxDataLength + 3, 18)),
      };
    }

    // 4. Default width
    return { wch: 20 };
  });
};

// --- PREDEFINED COLUMN CONFIGURATIONS ---

const getPurchasesColumnConfig = (): ColumnConfig[] => [
  { key: "transactionNumber", label: "No. Transaksi", type: "text", width: 15 },
  { key: "purchaseDate", label: "Tanggal Pembelian", type: "date", width: 18 },
  { key: "invoiceRef", label: "No. Invoice", type: "text", width: 15 },
  { key: "supplier.name", label: "Nama Supplier", type: "text", width: 25 },
  { key: "supplier.phone", label: "Nomor Telepon", type: "text", width: 16 },
  { key: "totalAmount", label: "Total Pembelian", type: "currency", width: 18 },
  { key: "paymentDueDate", label: "Jatuh Tempo", type: "date", width: 16 },
  {
    key: "status",
    label: "Status Pembayaran",
    type: "text",
    width: 18,
    formatter: (value: string) => (value === "LUNAS" ? "Lunas" : "Belum Lunas"),
  },
  { key: "memo", label: "Memo", type: "text", width: 30 },
  {
    key: "isDraft",
    label: "Status Draft",
    type: "text",
    width: 14,
    formatter: (isDraft: boolean) => (isDraft ? "Draft" : "Selesai"),
  },
  { key: "item.product.name", label: "Nama Produk", type: "text", width: 25 },
  { key: "item.product.sku", label: "SKU", type: "text", width: 12 },
  { key: "item.quantity", label: "Quantity", type: "number", width: 12 },
  {
    key: "item.costAtPurchase",
    label: "Harga Beli",
    type: "currency",
    width: 16,
  },
  {
    key: "item.discountPercentage",
    label: "Diskon (%)",
    type: "number",
    width: 12,
  },
  {
    key: "item.discountAmount",
    label: "Diskon (Rp)",
    type: "currency",
    width: 16,
  },
  { key: "item.unit", label: "Satuan", type: "text", width: 10 },
];

const getImportFriendlyColumnConfig = (): ColumnConfig[] => [
  { key: "purchaseDate", label: "Tanggal Pembelian", type: "date", width: 18 },
  { key: "supplier.name", label: "Nama Supplier", type: "text", width: 25 },
  { key: "supplier.phone", label: "Nomor Telepon", type: "text", width: 16 },
  { key: "supplier.email", label: "Email Supplier", type: "text", width: 25 },
  { key: "item.product.name", label: "Nama Produk", type: "text", width: 25 },
  { key: "item.quantity", label: "Quantity", type: "number", width: 12 },
  {
    key: "item.costAtPurchase",
    label: "Harga Beli",
    type: "currency",
    width: 16,
  },
  {
    key: "item.discountPercentage",
    label: "Diskon (%)",
    type: "number",
    width: 12,
  },
  {
    key: "item.discountAmount",
    label: "Diskon (Rp)",
    type: "currency",
    width: 16,
  },
  { key: "item.tax", label: "PPN (%)", type: "number", width: 10 },
  { key: "status", label: "Status Pembayaran", type: "text", width: 18 },
  { key: "memo", label: "Memo", type: "text", width: 30 },
  { key: "invoiceRef", label: "No. Invoice", type: "text", width: 15 },
  { key: "item.unit", label: "Satuan", type: "text", width: 10 },
];

// --- NEW MERGE LOGIC ---
const applyMergesAndVerticalAlign = (
  worksheet: XLSX.WorkSheet,
  data: any[],
  columns: ColumnConfig[],
  headerRowCount: number
) => {
  const mergeableColumns = columns
    .map((col, index) => ({ ...col, index }))
    .filter((col) => !col.key.startsWith("item."));

  if (data.length === 0) return;

  // Apply vertical alignment to all mergeable columns
  data.forEach((_, rowIndex) => {
    mergeableColumns.forEach((col) => {
      const cellRef = XLSX.utils.encode_cell({
        r: rowIndex + headerRowCount,
        c: col.index,
      });
      if (worksheet[cellRef]) {
        if (!worksheet[cellRef].s) worksheet[cellRef].s = {};
        if (!worksheet[cellRef].s.alignment)
          worksheet[cellRef].s.alignment = {};
        worksheet[cellRef].s.alignment.vertical = "center";
      }
    });
  });

  // Apply merges
  let mergeStartRow = 0;
  for (let i = 1; i <= data.length; i++) {
    if (
      i === data.length ||
      data[i].transactionNumber !== data[i - 1].transactionNumber
    ) {
      if (i - mergeStartRow > 1) {
        mergeableColumns.forEach((col) => {
          const start = { r: mergeStartRow + headerRowCount, c: col.index };
          const end = { r: i - 1 + headerRowCount, c: col.index };
          if (!worksheet["!merges"]) worksheet["!merges"] = [];
          worksheet["!merges"].push({ s: start, e: end });
        });
      }
      mergeStartRow = i;
    }
  }
};

// --- PURCHASES EXPORT ---

const createCombinedPurchasesSheet = (
  purchasesData: any[],
  reportPeriod: string,
  options: ExportOptions = {}
): XLSX.WorkSheet => {
  const sheetTitle = "Laporan Rinci Pembelian";
  const headerRowCount = 4;
  const columns = getPurchasesColumnConfig();

  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  // 1. Flatten data
  const processedData = purchasesData.flatMap((purchase) =>
    (purchase.items && purchase.items.length > 0 ? purchase.items : [{}]).map(
      (item: any) => ({
        ...purchase,
        item: { ...item, product: item.product || {} },
      })
    )
  );

  // Add grouping flags for borders
  processedData.forEach((row, index, arr) => {
    row.isFirstInGroup =
      index === 0 || row.transactionNumber !== arr[index - 1].transactionNumber;
    row.isLastInGroup =
      index === arr.length - 1 ||
      row.transactionNumber !== arr[index + 1].transactionNumber;
  });

  // 2. Add Sheet Title & Subtitle
  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  // 3. Add Table Headers
  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      SHEET_HEADER_STYLES.purchases
    );
  });

  // 4. Process and Add Data Rows
  const rows = processedData.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (value === null || value === undefined) return "";
      if (col.formatter) value = col.formatter(value);
      switch (col.type) {
        case "currency":
        case "number":
          return typeof value === "number" ? value : 0;
        case "date":
          return value ? new Date(value) : "";
        default:
          return String(value);
      }
    })
  );
  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  // 5. Style Data Rows with Borders
  processedData.forEach((item, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style = JSON.parse(
        JSON.stringify(
          rowIndex % 2 === 0
            ? CELL_STYLES.tableDataEven
            : CELL_STYLES.tableDataOdd
        )
      );

      // ADD THIS PART FOR NUMBER FORMATTING:
      if (col.type === "currency") {
        style.numFmt = '"Rp "#,##0_);("Rp "#,##0)'; // This formats numbers as 1,000,000
      }

      const border = { style: "thin", color: { rgb: "888888" } };
      if (item.isFirstInGroup) style.border = { ...style.border, top: border };
      if (item.isLastInGroup)
        style.border = { ...style.border, bottom: border };
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // 6. Apply Merges and Final Styling
  applyMergesAndVerticalAlign(
    worksheet,
    processedData,
    columns,
    headerRowCount
  );

  // 7. Set Column Widths (CUSTOMIZABLE)
  const colWidths = calculateColumnWidths(columns, processedData, options);
  setColumnWidths(worksheet, colWidths);

  setRowHeights(worksheet, { 1: 22, 2: 18, [headerRowCount]: 30 });
  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (processedData.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${processedData.length + headerRowCount}`
    );
  }

  return worksheet;
};

// Create a simple import-friendly export format
export const createPurchasesImportFriendlyWorksheet = (
  purchasesData: any[],
  options: ExportOptions = {}
): XLSX.WorkSheet => {
  console.log("[Export] Creating import-friendly purchases worksheet");

  const columns = getImportFriendlyColumnConfig();

  // Flatten data to simple rows
  const rows = purchasesData.flatMap((purchase) =>
    (purchase.items && purchase.items.length > 0 ? purchase.items : [{}]).map(
      (item: any) => [
        purchase.purchaseDate
          ? new Date(purchase.purchaseDate).toISOString().split("T")[0]
          : "",
        purchase.supplier?.name || "",
        purchase.supplier?.phone || "",
        purchase.supplier?.email || "",
        item.product?.name || "",
        item.quantity || 0,
        item.costAtPurchase || 0,
        item.discountPercentage || 0,
        item.discountAmount || 0,
        item.tax ? parseFloat(item.tax.replace("%", "")) || 0 : 0,
        purchase.status === "LUNAS" ? "Lunas" : "Belum Lunas",
        purchase.memo || "",
        purchase.invoiceRef || "",
        item.unit || "Pcs",
      ]
    )
  );

  console.log(
    `[Export] Generated ${rows.length} rows for import-friendly format`
  );

  // Create worksheet with headers and data
  const headers = columns.map((col) => col.label);
  const worksheetData = [headers, ...rows];
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths using the new system
  const colWidths = calculateColumnWidths(columns, [], options);
  setColumnWidths(worksheet, colWidths);

  return worksheet;
};

// --- EXPORTED FUNCTIONS ---

export const createPurchasesExcelReport = (
  purchasesData: any[],
  reportPeriod: string,
  options: ExportOptions = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  const combinedSheet = createCombinedPurchasesSheet(
    purchasesData,
    reportPeriod,
    options
  );
  XLSX.utils.book_append_sheet(
    workbook,
    combinedSheet,
    "Laporan Rinci Pembelian"
  );
  return workbook;
};

export const createPurchasesImportFriendlyExcelReport = (
  purchasesData: any[],
  options: ExportOptions = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  const worksheet = createPurchasesImportFriendlyWorksheet(
    purchasesData,
    options
  );
  XLSX.utils.book_append_sheet(workbook, worksheet, "Data Pembelian");
  return workbook;
};

// --- USAGE EXAMPLES ---

/*
// Example 1: Use default predefined widths
const workbook1 = createPurchasesExcelReport(data, "January 2024");

// Example 2: Override specific column widths
const workbook2 = createPurchasesExcelReport(data, "January 2024", {
  customColumnWidths: {
    "supplier.name": 35,        // Make supplier name column wider
    "item.product.name": 40,    // Make product name column wider
    "memo": 50,                 // Make memo column much wider
    "transactionNumber": 10     // Make transaction number column narrower
  }
});

// Example 3: Use auto-width calculation
const workbook3 = createPurchasesExcelReport(data, "January 2024", {
  useAutoWidth: true
});

// Example 4: Mix of custom widths and auto calculation
const workbook4 = createPurchasesExcelReport(data, "January 2024", {
  customColumnWidths: {
    "memo": 60,                 // Custom width for memo
    "supplier.name": 30         // Custom width for supplier
  },
  useAutoWidth: true            // Auto-calculate for other columns
});
*/
