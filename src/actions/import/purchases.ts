"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { generateSupplierId, generatePurchaseId } from "@/lib/generate-id";
import { createSystemNotification } from "@/lib/create-system-notification";
import { getNextTransactionNumber } from "@/actions/entities/purchases";
import * as XLSX from "xlsx";

interface ImportSummary {
  purchasesCreated: number;
  suppliersCreated: number;
  warehousesCreated: number;
  errors: string[];
}

interface ImportResult {
  success?: string;
  error?: string;
  summary: ImportSummary;
}

// Utility function to sanitize string inputs
const sanitizeString = (value: any): string => {
  if (value === null || value === undefined || value === "") return "";
  return String(value).trim().substring(0, 255);
};

// Utility function to sanitize number inputs
const sanitizeNumber = (value: any): number => {
  if (value === null || value === undefined || value === "") return 0;
  const num = Number(value);
  return isNaN(num) ? 0 : Math.max(0, num);
};

// Utility function to sanitize date inputs
const sanitizeDate = (value: any): Date => {
  if (value === null || value === undefined || value === "") {
    return new Date();
  }

  // Handle Excel date serial numbers
  if (typeof value === "number") {
    // Excel date serial number (days since 1900-01-01)
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(
      excelEpoch.getTime() + (value - 1) * 24 * 60 * 60 * 1000
    );
    return isNaN(date.getTime()) ? new Date() : date;
  }

  // Handle string dates
  if (typeof value === "string") {
    const date = new Date(value);
    return isNaN(date.getTime()) ? new Date() : date;
  }

  // Handle Date objects
  if (value instanceof Date) {
    return isNaN(value.getTime()) ? new Date() : value;
  }

  return new Date();
};

// Main import function for purchases
export const importPurchases = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  try {
    console.log("[IMPORT] Starting purchase import process");

    // Get effective user ID
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return {
        error: "Tidak terautentikasi",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["User tidak terautentikasi"],
        },
      };
    }

    // Parse Excel file
    const workbook = XLSX.read(arrayBuffer, { type: "array" });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    console.log(`[IMPORT] Parsed ${data.length} rows from Excel`);

    if (data.length < 2) {
      return {
        error: "File Excel kosong atau tidak memiliki data",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["File tidak memiliki data yang valid"],
        },
      };
    }

    // Get headers from first row and find header row (skip title rows)
    let headerRowIndex = 0;
    let headers: string[] = [];

    // Look for the row that contains "Tanggal Pembelian" (our main header)
    for (let i = 0; i < Math.min(5, data.length); i++) {
      const row = data[i] as string[];
      if (
        row &&
        row.some(
          (cell) => cell && cell.toString().includes("Tanggal Pembelian")
        )
      ) {
        headerRowIndex = i;
        headers = row.filter((cell) => cell && cell.toString().trim() !== "");
        break;
      }
    }

    if (headers.length === 0) {
      return {
        error:
          "Header tidak ditemukan. Pastikan file menggunakan template yang benar.",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["Header tidak valid"],
        },
      };
    }

    console.log(
      `[IMPORT] Headers found at row ${headerRowIndex + 1}:`,
      headers
    );
    const dataRows = data.slice(headerRowIndex + 1);

    // Convert to objects and filter valid rows
    const purchaseData = (dataRows as any[][])
      .map((row: any[], index: number) => {
        const obj: any = {};
        headers.forEach((header, i) => {
          obj[header] = row[i];
        });
        obj._rowIndex = headerRowIndex + index + 2; // Actual Excel row number
        return obj;
      })
      .filter((row) => {
        // Filter out empty rows - check required fields
        const tanggalPembelian = sanitizeString(row["Tanggal Pembelian"]);
        const namaProduk = sanitizeString(row["Nama Produk"]);
        const quantity = sanitizeNumber(row["Quantity"]);
        const hargaBeli = sanitizeNumber(row["Harga Beli"]);

        return tanggalPembelian && namaProduk && quantity > 0 && hargaBeli > 0;
      });

    console.log(
      `[IMPORT] Filtered to ${purchaseData.length} valid purchase rows`
    );

    if (purchaseData.length === 0) {
      return {
        error: "Tidak ada data pembelian yang valid ditemukan",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["Tidak ada baris data yang memenuhi kriteria minimum"],
        },
      };
    }

    // Pre-validate all products before processing any rows
    console.log("[IMPORT] Validating product availability...");
    const uniqueProductNames = new Set<string>();

    // Collect all unique product names from the import data
    purchaseData.forEach((row) => {
      const productName = sanitizeString(row["Nama Produk"]);
      if (productName) {
        uniqueProductNames.add(productName);
      }
    });

    // Check if all products exist in the database
    const existingProducts = await db.product.findMany({
      where: {
        name: {
          in: Array.from(uniqueProductNames),
        },
        userId: effectiveUserId,
      },
      select: {
        id: true,
        name: true,
      },
    });

    const existingProductMap = new Map(
      existingProducts.map((p) => [p.name, p.id])
    );
    const missingProducts: string[] = [];

    uniqueProductNames.forEach((productName) => {
      if (!existingProductMap.has(productName)) {
        missingProducts.push(productName);
      }
    });

    if (missingProducts.length > 0) {
      const errorMessage = `Produk berikut tidak ditemukan di sistem: ${missingProducts.join(", ")}. Silakan buat produk tersebut terlebih dahulu.`;

      return {
        error: errorMessage,
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: [errorMessage],
        },
      };
    }

    console.log(
      `[IMPORT] Product validation passed - all ${uniqueProductNames.size} products found`
    );

    if (purchaseData.length > 100) {
      return {
        error: "Terlalu banyak data. Maksimal 100 transaksi per import",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["Jumlah data melebihi batas maksimal 100 transaksi"],
        },
      };
    }

    // Process import - simpler approach without complex batching
    let totalPurchasesCreated = 0;
    let totalSuppliersCreated = 0;
    const allErrors: string[] = [];

    console.log(`[IMPORT] Processing ${purchaseData.length} purchase rows`);

    // Process each row individually
    for (const row of purchaseData) {
      try {
        const result = await db.$transaction(async (tx) => {
          // Extract and validate data
          const purchaseDate = sanitizeDate(row["Tanggal Pembelian"]);
          const productName = sanitizeString(row["Nama Produk"]);
          const quantity = sanitizeNumber(row["Quantity"]);
          const costPrice = sanitizeNumber(row["Harga Beli"]);
          const supplierName = sanitizeString(row["Nama Supplier"]);
          const supplierPhone = sanitizeString(row["Nomor Telepon"]);
          const supplierEmail = sanitizeString(row["Email Supplier"]);
          const discountPercentage = sanitizeNumber(row["Diskon (%)"]);
          const discountAmount = sanitizeNumber(row["Diskon (Rp)"]);
          const taxPercentage = sanitizeNumber(row["PPN (%)"]);
          const statusValue = sanitizeString(row["Status Pembayaran"]);
          const memo = sanitizeString(row["Memo"]);
          const invoiceRef = sanitizeString(row["No. Invoice"]);
          const unit = sanitizeString(row["Satuan"]) || "Pcs";

          // Get product ID from our pre-validated map
          const productId = existingProductMap.get(productName);
          if (!productId) {
            throw new Error(`Produk "${productName}" tidak ditemukan`);
          }

          // Handle supplier creation/lookup
          let supplierId: string | null = null;
          let suppliersCreated = 0;

          if (supplierName) {
            let supplier = await tx.supplier.findFirst({
              where: {
                name: supplierName,
                userId: effectiveUserId,
              },
            });

            if (!supplier) {
              const newSupplierId = await generateSupplierId();
              supplier = await tx.supplier.create({
                data: {
                  id: newSupplierId,
                  name: supplierName,
                  phone: supplierPhone || null,
                  email: supplierEmail || null,
                  userId: effectiveUserId,
                },
              });
              suppliersCreated = 1;
            }
            supplierId = supplier.id;
          }

          // Calculate totals
          const subtotal = quantity * costPrice;
          const finalDiscountAmount =
            discountAmount > 0
              ? discountAmount
              : (subtotal * discountPercentage) / 100;
          const afterDiscount = subtotal - finalDiscountAmount;
          const taxAmount = (afterDiscount * taxPercentage) / 100;
          const totalAmount = afterDiscount + taxAmount;

          // Parse status
          const status = statusValue === "Lunas" ? "LUNAS" : "BELUM_LUNAS";

          // Generate new purchase ID and transaction number
          const purchaseId = await generatePurchaseId(effectiveUserId);
          const transactionNumberResult = await getNextTransactionNumber(
            "TRX",
            purchaseDate
          );
          const transactionNumber =
            transactionNumberResult || `BELI-${Date.now()}`;

          // Create purchase
          const purchase = await tx.purchase.create({
            data: {
              id: purchaseId,
              purchaseDate,
              totalAmount,
              transactionNumber,
              invoiceRef: invoiceRef || null,
              memo: memo || null,
              status: status as any,
              userId: effectiveUserId,
              supplierId,
              isDraft: false,
            },
          });

          // Create purchase item
          await tx.purchaseItem.create({
            data: {
              quantity,
              costAtPurchase: costPrice,
              unit,
              discountPercentage:
                discountPercentage > 0 ? discountPercentage : null,
              discountAmount:
                finalDiscountAmount > 0 ? finalDiscountAmount : null,
              tax: taxPercentage > 0 ? `${taxPercentage}%` : null,
              purchaseId: purchase.id,
              productId,
            },
          });

          // Update product stock
          await tx.product.update({
            where: { id: productId },
            data: {
              stock: {
                increment: quantity,
              },
            },
          });

          return {
            purchasesCreated: 1,
            suppliersCreated,
          };
        });

        // Accumulate results
        totalPurchasesCreated += result.purchasesCreated;
        totalSuppliersCreated += result.suppliersCreated;
      } catch (error) {
        console.error(`[IMPORT] Error processing row ${row._rowIndex}:`, error);
        const errorMessage =
          error instanceof Error ? error.message : "Error tidak diketahui";
        allErrors.push(`Baris ${row._rowIndex}: ${errorMessage}`);
      }
    }

    // Return final results
    const finalResult = {
      purchasesCreated: totalPurchasesCreated,
      suppliersCreated: totalSuppliersCreated,
      warehousesCreated: 0, // Not used in this simplified version
      errors: allErrors,
    };

    // Create notifications based on import results
    try {
      if (allErrors.length > 0 && totalPurchasesCreated === 0) {
        // Complete failure
        await createSystemNotification(
          "error",
          "Import Pembelian Gagal",
          `Import pembelian gagal sepenuhnya. ${allErrors.length} error ditemukan.`,
          false
        );

        return {
          error: "Import gagal",
          summary: finalResult,
        };
      } else if (allErrors.length > 0 && totalPurchasesCreated > 0) {
        // Partial success
        await createSystemNotification(
          "warning",
          "Import Pembelian Sebagian Berhasil",
          `Import pembelian selesai dengan ${totalPurchasesCreated} pembelian berhasil diimpor, namun ${allErrors.length} baris gagal diproses.`,
          false
        );
      } else {
        // Complete success
        await createSystemNotification(
          "success",
          "Import Pembelian Berhasil",
          `Import pembelian berhasil! ${totalPurchasesCreated} pembelian berhasil diimpor. ${totalSuppliersCreated} supplier baru telah dibuat.`,
          false
        );
      }
    } catch (notificationError) {
      console.error("Failed to create import notification:", notificationError);
    }

    return {
      success: `Import berhasil! ${totalPurchasesCreated} pembelian berhasil diimpor.`,
      summary: finalResult,
    };
  } catch (error) {
    console.error("Import error:", error);

    // Create error notification
    try {
      await createSystemNotification(
        "error",
        "Import Pembelian Error",
        `Terjadi kesalahan sistem saat mengimpor pembelian: ${error instanceof Error ? error.message : "Unknown error"}`,
        false
      );
    } catch (notificationError) {
      console.error("Failed to create error notification:", notificationError);
    }

    return {
      error: "Gagal memproses file import",
      summary: {
        purchasesCreated: 0,
        suppliersCreated: 0,
        warehousesCreated: 0,
        errors: [error instanceof Error ? error.message : "Unknown error"],
      },
    };
  }
};
