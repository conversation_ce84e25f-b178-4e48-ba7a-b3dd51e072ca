"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Calendar,
  Settings,
  FileSpreadsheet,
  Clock,
  Sliders,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { getPurchaseReportDataWithFilters } from "@/actions/reports";
import {
  createPurchasesExcelReport,
  createPurchasesImportFriendlyExcelReport,
} from "@/utils/exports/purchasesExport";
import {
  generateDateStringForFilename,
  validateExportPeriod,
} from "@/utils/dateUtils";

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  includeSummary: boolean;
  includeCharts: boolean;
  importFriendly: boolean;
}

interface PurchaseExportButtonProps {
  onExport?: () => void;
}

export const PurchaseExportButton: React.FC<PurchaseExportButtonProps> = ({
  onExport,
}) => {
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    includeSummary: false,
    includeCharts: false,
    importFriendly: false,
  });

  const handleAdvancedExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      const validationError = validateExportPeriod(exportConfig.reportType, {
        selectedDate: exportConfig.selectedDate,
        selectedMonth: exportConfig.selectedMonth,
        selectedYear: exportConfig.selectedYear,
      });

      if (validationError) {
        toast.error(validationError);
        return;
      }

      setExportProgress(20);

      let startDate: Date | undefined;
      let endDate: Date | undefined;
      let dateRange: string = "monthly";
      let periodLabel: string = "Data Pembelian";

      if (exportConfig.reportType === "harian") {
        startDate = new Date(exportConfig.selectedDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(exportConfig.selectedDate);
        endDate.setHours(23, 59, 59, 999);
        dateRange = "daily";
        periodLabel = `Harian - ${startDate.toLocaleDateString("id-ID")}`;
      } else if (exportConfig.reportType === "bulanan") {
        const year = exportConfig.selectedYear || new Date().getFullYear();
        const month = exportConfig.selectedMonth || new Date().getMonth();
        startDate = new Date(year, month, 1);
        endDate = new Date(year, month + 1, 0, 23, 59, 59, 999);
        dateRange = "monthly";
        periodLabel = `Bulanan - ${startDate.toLocaleDateString("id-ID", { month: "long", year: "numeric" })}`;
      } else if (exportConfig.reportType === "tahunan") {
        const year = exportConfig.selectedYear || new Date().getFullYear();
        startDate = new Date(year, 0, 1);
        endDate = new Date(year, 11, 31, 23, 59, 59, 999);
        dateRange = "yearly";
        periodLabel = `Tahunan - ${year}`;
      }

      setExportProgress(40);

      const purchaseResult = await getPurchaseReportDataWithFilters({
        dateRange,
        startDate,
        endDate,
        supplier: undefined,
      });

      setExportProgress(70);

      if (purchaseResult.error) {
        console.error("Purchase data fetch error:", purchaseResult.error);
        throw new Error(purchaseResult.error);
      }

      if (!purchaseResult.data || purchaseResult.data.length === 0) {
        toast.error(
          "Tidak ada data pembelian untuk diekspor pada periode yang dipilih"
        );
        return;
      }

      setExportProgress(85);

      const workbook = exportConfig.importFriendly
        ? createPurchasesImportFriendlyExcelReport(purchaseResult.data)
        : createPurchasesExcelReport(purchaseResult.data, periodLabel, {
            companyName: "KivaPOS",
            reportTitle: "Laporan Pembelian",
          });

      const excelBuffer = XLSX.write(workbook, {
        type: "array",
        bookType: "xlsx",
      });

      setExportProgress(100);

      const dateString = generateDateStringForFilename(
        exportConfig.reportType,
        {
          selectedDate: exportConfig.selectedDate,
          selectedMonth: exportConfig.selectedMonth,
          selectedYear: exportConfig.selectedYear,
        }
      );

      const fileName = exportConfig.importFriendly
        ? `data-pembelian-import-${dateString}.xlsx`
        : `laporan-pembelian-${exportConfig.reportType}-${dateString}.xlsx`;
      const blob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success("Export berhasil!");
      setShowExportDialog(false);

      if (onExport) {
        onExport();
      }
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor data");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  return (
    <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
        >
          <Download className="h-4 w-4" />
          <span className="hidden sm:inline">Export</span>
        </Button>
      </DialogTrigger>

      <DialogContent className="w-[95vw] sm:w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] flex flex-col rounded-lg border-0 shadow-2xl">
        <DialogHeader className="flex-shrink-0 px-4 sm:px-6 pt-6 pb-4">
          <DialogTitle className="flex items-center gap-3 text-lg sm:text-xl font-semibold">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Settings className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <span>Export Data Pembelian</span>
          </DialogTitle>
          <DialogDescription className="text-gray-600 dark:text-gray-400 mt-2">
            Pilih periode dan format yang ingin diekspor untuk data pembelian
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-4 sm:px-6 pb-4 space-y-6">
          {/* Period Selection */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <Label className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                Periode Laporan
              </Label>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              {[
                {
                  value: "harian",
                  label: "Harian",
                  icon: Calendar,
                  desc: "Per hari",
                },
                {
                  value: "bulanan",
                  label: "Bulanan",
                  icon: Calendar,
                  desc: "Per bulan",
                },
                {
                  value: "tahunan",
                  label: "Tahunan",
                  icon: Calendar,
                  desc: "Per tahun",
                },
              ].map((type) => (
                <Card
                  key={type.value}
                  className={`cursor-pointer transition-all duration-200 hover:shadow-md border-2 ${
                    exportConfig.reportType === type.value
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-950 shadow-md"
                      : "border-gray-200 dark:border-gray-700 hover:border-blue-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                  }`}
                  onClick={() =>
                    setExportConfig((prev) => ({
                      ...prev,
                      reportType: type.value as any,
                    }))
                  }
                >
                  <div className="p-2 sm:p-3 text-center">
                    <div
                      className={`mx-auto w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center mb-1.5 ${
                        exportConfig.reportType === type.value
                          ? "bg-blue-100 dark:bg-blue-900"
                          : "bg-gray-100 dark:bg-gray-800"
                      }`}
                    >
                      <type.icon
                        className={`h-3 w-3 sm:h-4 sm:w-4 ${
                          exportConfig.reportType === type.value
                            ? "text-blue-600 dark:text-blue-400"
                            : "text-gray-600 dark:text-gray-400"
                        }`}
                      />
                    </div>
                    <div className="text-xs sm:text-sm font-semibold text-gray-900 dark:text-gray-100">
                      {type.label}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5 hidden sm:block">
                      {type.desc}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Date/Period Selection */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <Label className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                Pilih Periode
              </Label>
            </div>

            {exportConfig.reportType === "harian" && (
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border">
                <DatePicker
                  date={exportConfig.selectedDate}
                  setDate={(date) => {
                    setExportConfig((prev) => ({
                      ...prev,
                      selectedDate: date || new Date(),
                    }));
                  }}
                  placeholder="Pilih tanggal"
                  className="w-full h-10 cursor-pointer"
                />
              </div>
            )}

            {exportConfig.reportType === "bulanan" && (
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-xs text-gray-600 dark:text-gray-400 mb-2 block">
                      Bulan
                    </Label>
                    <Select
                      value={exportConfig.selectedMonth?.toString() || ""}
                      onValueChange={(value) => {
                        setExportConfig((prev) => ({
                          ...prev,
                          selectedMonth: parseInt(value),
                        }));
                      }}
                    >
                      <SelectTrigger className="!h-10 w-full">
                        <SelectValue placeholder="Pilih bulan" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 12 }, (_, i) => (
                          <SelectItem key={i} value={i.toString()}>
                            {new Date(0, i).toLocaleDateString("id-ID", {
                              month: "long",
                            })}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-xs text-gray-600 dark:text-gray-400 mb-2 block">
                      Tahun
                    </Label>
                    <Input
                      type="number"
                      min="2020"
                      max="2030"
                      placeholder="Tahun"
                      value={exportConfig.selectedYear || ""}
                      onChange={(e) => {
                        setExportConfig((prev) => ({
                          ...prev,
                          selectedYear:
                            parseInt(e.target.value) ||
                            new Date().getFullYear(),
                        }));
                      }}
                      className="h-10"
                    />
                  </div>
                </div>
              </div>
            )}

            {exportConfig.reportType === "tahunan" && (
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border">
                <Label className="text-xs text-gray-600 dark:text-gray-400 mb-2 block">
                  Tahun
                </Label>
                <Input
                  type="number"
                  min="2020"
                  max="2030"
                  placeholder="Tahun"
                  value={exportConfig.selectedYear || ""}
                  onChange={(e) => {
                    setExportConfig((prev) => ({
                      ...prev,
                      selectedYear:
                        parseInt(e.target.value) || new Date().getFullYear(),
                    }));
                  }}
                  className="w-full h-10"
                />
              </div>
            )}
          </div>

          <Separator className="my-6" />

          {/* Format Selection - Excel Only */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <FileSpreadsheet className="h-4 w-4 text-gray-500" />
              <Label className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                Format Export
              </Label>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950 border-2 border-green-200 dark:border-green-800 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <FileSpreadsheet className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div className="flex-1">
                  <div className="font-semibold text-green-900 dark:text-green-100">
                    Excel (.xlsx)
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 mt-1">
                    Dengan sheet 'Data Produk' dan 'Info Dokumen'
                  </div>
                </div>
                <div className="hidden sm:block">
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full font-medium">
                    Recommended
                  </span>
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Additional Options */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Sliders className="h-4 w-4 text-gray-500" />
              <Label className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                Opsi Tambahan
              </Label>
            </div>

            <div className="space-y-3">
              <div className="flex items-start space-x-3 p-3 rounded-lg border bg-white dark:bg-gray-800 transition-colors relative">
                <Checkbox
                  id="importFriendly"
                  checked={false}
                  disabled={true}
                  className="mt-0.5"
                />
                <div className="flex-1">
                  <Label
                    htmlFor="importFriendly"
                    className="text-sm font-medium text-gray-500 dark:text-gray-400 cursor-not-allowed"
                  >
                    Format untuk Import
                  </Label>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Format data yang siap untuk diimport ke sistem lain
                  </p>
                </div>
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-xs px-2 py-1 rounded-full font-medium">
                  Coming Soon
                </span>
              </div>

              <div className="flex items-start space-x-3 p-3 rounded-lg border bg-white dark:bg-gray-800 transition-colors relative">
                <Checkbox
                  id="includeSummary"
                  checked={false}
                  disabled={true}
                  className="mt-0.5"
                />
                <div className="flex-1">
                  <Label
                    htmlFor="includeSummary"
                    className="text-sm font-medium text-gray-500 dark:text-gray-400 cursor-not-allowed"
                  >
                    Sertakan ringkasan data
                  </Label>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Tambahkan sheet ringkasan dengan statistik dan grafik
                  </p>
                </div>
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-xs px-2 py-1 rounded-full font-medium">
                  Coming Soon
                </span>
              </div>
            </div>
          </div>

          {/* Export Progress */}
          {isExporting && (
            <div className="space-y-3 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center gap-2">
                <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Sedang mengekspor data...
                </span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-blue-800 dark:text-blue-200">
                  <span>Progress Export</span>
                  <span className="font-medium">{exportProgress}%</span>
                </div>
                <Progress
                  value={exportProgress}
                  className="w-full h-2 bg-blue-100 dark:bg-blue-900"
                />
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons - Fixed at bottom */}
        <div className="flex-shrink-0 flex flex-col sm:flex-row gap-3 sm:justify-between px-4 sm:px-6 py-4 border-t bg-gray-50 dark:bg-gray-900">
          <Button
            variant="outline"
            onClick={() => setShowExportDialog(false)}
            className="order-2 sm:order-1 w-full sm:w-auto cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            disabled={isExporting}
          >
            Batal
          </Button>
          <Button
            onClick={handleAdvancedExport}
            disabled={isExporting}
            className="order-1 sm:order-2 w-full sm:w-auto flex items-center justify-center gap-2 cursor-pointer bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 transition-colors shadow-sm"
          >
            {isExporting ? (
              <>
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                Mengekspor...
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                Export Data Pembelian
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
