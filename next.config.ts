import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
      },
      {
        protocol: "https",
        hostname: "kivapos-s3-storage.s3.ap-southeast-3.amazonaws.com",
        pathname: "/**",
        port: "",
      },
      {
        protocol: "https",
        hostname: "dfy2n0rasnsczhyv.public.blob.vercel-storage.com",
      },
    ],
  },
};

export default nextConfig;
